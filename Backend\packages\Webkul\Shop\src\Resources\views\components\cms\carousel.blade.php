<v-cms-carousel
    src="{{ $src }}"
    title="{{ $title }}"
    navigation-link="{{ $navigationLink ?? '' }}"
>
    <x-shop::shimmer.cms.carousel :navigation-link="$navigationLink ?? false" />
</v-cms-carousel>

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-cms-carousel-template"
    >
        <div
            class="mt-20 max-md:mt-8 max-sm:mt-7 overflow-hidden"
            v-if="! isLoading && cmsPages.length"
        >
            <div class="flex justify-center">
                <h2 class="font-dmserif text-3xl max-md:text-2xl max-sm:text-xl" style="font-family: 'AlimamaShuHeiTi-Bold', 'DM Serif Display', serif; letter-spacing: 1.6px;">
                    @{{ title }}
                </h2>
            </div>

            <div class="flex items-start gap-12 mt-14 max-lg:flex-col max-lg:gap-8">
                <!-- 左侧 ABOUT MLK+ 区域 -->
                <div class="flex-1 max-w-md" style="margin-left: 9rem !important;margin-right: 2rem !important;">
                    <div class="bg-white p-8">
                        <h2 class="font-semibold text-4xl text-black leading-[56px] text-left mb-6" style="font-family: PingFangSC, PingFang SC;">@lang('shop::app.home.index.about_mlk')</h2>
                        <p class="font-normal text-base text-black leading-6 text-left mb-4" style="font-family: PingFangSC, PingFang SC;">
                            @lang('shop::app.home.index.about_mlk_description')
                        </p>
                        <button class="border border-black rounded px-6 py-2 text-sm font-medium hover:bg-black hover:text-white transition-colors">
                            @lang('shop::app.home.index.show-all')
                        </button>
                    </div>
                </div>

                                <!-- 右侧 DISCOVER 区域 -->
                <div class="flex-1 overflow-hidden relative">
                    
                    <!-- 白色渐变遮罩 -->
                    <div class="absolute right-0 top-0 bottom-0 w-48 bg-gradient-to-l from-white dark:from-gray-900 via-white/90 dark:via-gray-900/90 to-transparent z-50 pointer-events-none"></div>
                    
                    <!-- 额外的渐变遮罩，更加明显 -->
                    <div class="absolute right-0 top-0 bottom-0 w-24 bg-gradient-to-l from-white dark:from-gray-900 to-transparent z-40 pointer-events-none"></div>
                    
                    <div
                        ref="swiperContainer"
                        class="flex gap-8 pb-2.5 [&>*]:flex-[0] overflow-x-auto overflow-y-hidden scroll-smooth scrollbar-hide max-md:gap-7 max-sm:gap-4 max-md:pb-0 max-md:whitespace-nowrap -mx-4 lg:-mx-8 xl:-mx-12"
                    >
                <div
                    v-for="page in cmsPages"
                    :key="page.id"
                    class="min-w-[291px] max-md:h-fit max-md:min-w-56 max-sm:min-w-[192px] bg-white dark:bg-gray-900 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                >
                    <div class="aspect-[366/274] w-full overflow-hidden bg-gray-100 dark:bg-gray-800">
                        <img
                            v-if="page.thumbnail"
                            :src="page.thumbnail"
                            :alt="page.page_title"
                            class="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                        />
                        <div
                            v-else
                            class="flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700"
                        >
                            <span class="icon-image text-4xl text-gray-400 dark:text-gray-500"></span>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <!-- CMS page title with 2-line limit -->
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-3 line-clamp-2 leading-tight">
                            @{{ page.page_title }}
                        </h3>
                        
                        <!-- CMS page content excerpt -->
                        <div 
                            class="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3"
                            v-html="getExcerpt(page.content || page.html_content)"
                        >
                        </div>
                        
                        <!-- Read more button -->
                        <a
                            :href="page.url"
                            class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-colors duration-200"
                        >
                            @lang('shop::app.components.cms.carousel.read-more')
                            <span class="icon-arrow-right ml-1 text-xs"></span>
                        </a>
                    </div>
                </div>
                    </div>
                </div>
            </div>

            <a
                :href="navigationLink"
                class="secondary-button mx-auto mt-5 block w-max rounded-2xl px-11 py-3 text-center text-base max-lg:mt-0 max-lg:hidden max-lg:py-3.5 max-md:rounded-lg"
                :aria-label="title"
                v-if="navigationLink"
            >
                @lang('shop::app.components.cms.carousel.view-all')
            </a>
        </div>

        <!-- Loading shimmer -->
        <template v-if="isLoading">
            <x-shop::shimmer.cms.carousel :navigation-link="$navigationLink ?? false" />
        </template>
    </script>

    <script type="module">
        app.component('v-cms-carousel', {
            template: '#v-cms-carousel-template',

            props: [
                'src',
                'title',
                'navigationLink',
            ],

            data() {
                return {
                    isLoading: true,

                    cmsPages: [],

                    offset: 323,

                    isScreenMax2xl: window.innerWidth <= 1440,
                };
            },

            mounted() {
                this.getCMSPages();
            },

            created() {
                window.addEventListener('resize', this.updateScreenSize);
            },

            beforeDestroy() {
                window.removeEventListener('resize', this.updateScreenSize);
            },

            methods: {
                getCMSPages() {
                    this.$axios.get(this.src)
                        .then(response => {
                            this.isLoading = false;

                            this.cmsPages = response.data.data;
                        }).catch(error => {
                            console.log(error);
                            this.isLoading = false;
                        });
                },

                updateScreenSize() {
                    this.isScreenMax2xl = window.innerWidth <= 1440;
                },

                swipeLeft() {
                    const container = this.$refs.swiperContainer;

                    container.scrollLeft -= this.offset;
                },

                swipeRight() {
                    const container = this.$refs.swiperContainer;

                    // Check if scroll reaches the end
                    if (container.scrollLeft + container.clientWidth >= container.scrollWidth) {
                        // Reset scroll to the beginning
                        container.scrollLeft = 0;
                    } else {
                        // Scroll to the right
                        container.scrollLeft += this.offset;
                    }
                },

                getExcerpt(content) {
                    if (!content) return '';
                    
                    // Remove HTML tags
                    const textContent = content.replace(/<[^>]*>/g, '');
                    
                    // Limit character count
                    const maxLength = 120;
                    if (textContent.length <= maxLength) {
                        return textContent;
                    }
                    
                    return textContent.substring(0, maxLength) + '...';
                },
            },
        });
    </script>
@endPushOnce
