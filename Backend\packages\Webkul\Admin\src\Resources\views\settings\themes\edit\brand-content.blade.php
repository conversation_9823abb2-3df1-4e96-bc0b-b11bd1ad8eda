<v-brand-content :errors="errors">
    <x-admin::shimmer.settings.themes.brand-content />
</v-brand-content>

@pushOnce('scripts')
    <script type="text/x-template" id="v-brand-content-template">
        <div class="flex flex-1 flex-col gap-2 max-xl:flex-auto">
            <div class="box-shadow rounded bg-white p-4 dark:bg-gray-900">
                <div class="flex items-center justify-between gap-x-2.5">
                    <div class="flex flex-col gap-1">
                        <p class="text-base font-semibold text-gray-800 dark:text-white">
                            @lang('admin::app.settings.themes.edit.brand-content')
                        </p>
                        
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-300">
                            @lang('admin::app.settings.themes.edit.brand-content-description')
                        </p>
                    </div>

                    <!-- Add Brand Button -->
                    <div
                        class="secondary-button"
                        @click="$refs.addBrandModal.toggle()"
                    >
                        @lang('admin::app.settings.themes.edit.brand-add-btn')
                    </div>
                </div>

                <template v-for="(deletedBrand, index) in deletedBrands">
                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[deleted_brands]['+ index +'][image]'"
                        :value="deletedBrand.image"
                    />
                </template>

                <!-- Hidden inputs for existing brands -->
                <template v-for="(brand, index) in brands">
                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options]['+ index +'][name]'"
                        :value="brand.name"
                    />

                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options]['+ index +'][url]'"
                        :value="brand.url"
                    />

                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options]['+ index +'][image]'"
                        :value="brand.image"
                    />
                </template>

                <div
                    class="grid pt-4"
                    v-if="brands.length"
                    v-for="(brand, index) in brands"
                >
                
                    <!-- Brand Details -->
                    <div 
                        class="flex cursor-pointer justify-between gap-2.5 py-5"
                        :class="{
                            'border-b border-slate-300 dark:border-gray-800': index < brands.length - 1
                        }"
                    >
                        <div class="flex gap-2.5">
                            <!-- Brand Image -->
                            <div class="h-16 w-16 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                                <img
                                    v-if="brand._preview || (brand.image && typeof brand.image === 'string' && !brand.image.startsWith('blob:'))"
                                    :src="brand._preview || '{{ config('app.url') }}/' + brand.image"
                                    :alt="brand.name"
                                    class="h-full w-full object-cover"
                                />
                                <div
                                    v-else
                                    class="flex h-full w-full items-center justify-center bg-gray-100 dark:bg-gray-800"
                                >
                                    <span class="icon-image text-2xl text-gray-400 dark:text-gray-500"></span>
                                </div>
                            </div>

                            <!-- Brand Info -->
                            <div class="grid place-content-start gap-1.5">
                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-name'): 

                                    <span class="text-gray-800 dark:text-white font-medium">
                                        @{{ brand.name }}
                                    </span>
                                </p>

                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-url'): 

                                    <span class="text-gray-600 transition-all dark:text-gray-300">
                                        <a
                                            v-if="brand.url"
                                            :href="brand.url"
                                            target="_blank"
                                            class="text-blue-600 transition-all hover:underline"
                                        >
                                            @{{ brand.url }}
                                        </a>
                                        <span v-else class="text-gray-400">
                                            @lang('admin::app.settings.themes.edit.no-url')
                                        </span>
                                    </span>
                                </p>

                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-image'): 

                                    <span class="text-gray-600 transition-all dark:text-gray-300">
                                        <a
                                            v-if="brand.image && typeof brand.image === 'string' && !brand.image.startsWith('blob:')"
                                            :href="'{{ config('app.url') }}/' + brand.image"
                                            target="_blank"
                                            class="text-blue-600 transition-all hover:underline ltr:ml-2 rtl:mr-2"
                                        >
                                            <span :ref="'imageName_' + index">
                                                @{{ brand.image }}
                                            </span>
                                        </a>
                                        <span v-else-if="brand._preview" class="text-blue-600">
                                            @lang('admin::app.settings.themes.edit.preview-image')
                                        </span>
                                        <span v-else class="text-gray-400">
                                            @lang('admin::app.settings.themes.edit.no-image')
                                        </span>
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="grid place-content-start gap-1 text-right">
                            <p 
                                class="cursor-pointer text-red-600 transition-all hover:underline"
                                @click="removeBrand(brand, index)"
                            > 
                                @lang('admin::app.settings.themes.edit.delete')
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Empty Page -->
                <div
                    class="grid justify-center justify-items-center gap-3.5 px-2.5 py-10"
                    v-else
                >
                    <img
                        class="h-[120px] w-[120px] p-2 dark:mix-blend-exclusion dark:invert"
                        src="{{ bagisto_asset('images/empty-placeholders/default.svg') }}"
                        alt="@lang('admin::app.settings.themes.edit.brand-content')"
                    >

                    <div class="flex flex-col items-center gap-1.5">
                        <p class="text-base font-semibold text-gray-400">
                            @lang('admin::app.settings.themes.edit.brand-add-btn')
                        </p>
                        
                        <p class="text-gray-400">
                            @lang('admin::app.settings.themes.edit.brand-content-description')
                        </p>
                    </div>
                </div>
            </div>

            <x-admin::form
                v-slot="{ meta, errors, handleSubmit }"
                as="div"
            >
                <form 
                    @submit="handleSubmit($event, saveBrand)"
                    enctype="multipart/form-data"
                    ref="createBrandForm"
                >
                    <x-admin::modal ref="addBrandModal">
                        <!-- Modal Header -->
                        <x-slot:header>
                            <p class="text-lg font-bold text-gray-800 dark:text-white">
                                @lang('admin::app.settings.themes.edit.add-brand')
                            </p>
                        </x-slot>

                        <!-- Modal Content -->
                        <x-slot:content>
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.themes.edit.brand-name')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    name="{{ $currentLocale->code }}[name]"
                                    rules="required"
                                    :placeholder="trans('admin::app.settings.themes.edit.brand-name')"
                                    :label="trans('admin::app.settings.themes.edit.brand-name')"
                                />

                                <x-admin::form.control-group.error control-name="{{ $currentLocale->code }}[name]" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    @lang('admin::app.settings.themes.edit.brand-url')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    name="{{ $currentLocale->code }}[url]"
                                    :placeholder="trans('admin::app.settings.themes.edit.brand-url')"
                                    :label="trans('admin::app.settings.themes.edit.brand-url')"
                                />

                                <x-admin::form.control-group.error control-name="{{ $currentLocale->code }}[url]" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.themes.edit.brand-image')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="image"
                                    name="brand_image"
                                    rules="required"
                                    :is-multiple="false"
                                />

                                <x-admin::form.control-group.error control-name="brand_image" />
                            </x-admin::form.control-group>

                            <p class="text-xs text-gray-600 dark:text-gray-300">
                                @lang('admin::app.settings.themes.edit.brand-image-size')
                            </p>
                        </x-slot>

                        <!-- Modal Footer -->
                        <x-slot:footer>
                            <div class="flex items-center justify-end gap-2.5">
                                <button
                                    type="button"
                                    class="secondary-button"
                                    @click="$refs.addBrandModal.toggle()"
                                >
                                    @lang('admin::app.settings.themes.edit.cancel')
                                </button>

                                <button
                                    type="submit"
                                    class="primary-button"
                                >
                                    @lang('admin::app.settings.themes.edit.add-brand')
                                </button>
                            </div>
                        </x-slot>
                    </x-admin::modal>
                </form>
            </x-admin::form>
        </div>
    </script>

    <script type="module">
        app.component('v-brand-content', {
            template: '#v-brand-content-template',
            props: ['errors'],
            data() {
                return {
                    options: @json($theme->translate($currentLocale->code)['options'] ?? null),
                    brands: @json($theme->translate($currentLocale->code)['options']['brands'] ?? []),
                    deletedBrands: [],
                };
            },
            
            created() {
                // 确保brands是数组
                if (!Array.isArray(this.brands)) {
                    this.brands = [];
                }
            },
            methods: {
                saveBrand(params, { resetForm, setErrors }) {
                    let formData = new FormData(this.$refs.createBrandForm);

                    try {
                        const brandImage = formData.get("brand_image");

                        if (!brandImage) {
                            throw new Error("品牌图片是必填项");
                        }

                        const newBrand = {
                            name: formData.get("{{ $currentLocale->code }}[name]"),
                            url: formData.get("{{ $currentLocale->code }}[url]") || '',
                            brand_image: brandImage,
                        };

                        this.brands.push(newBrand);

                        if (brandImage instanceof File) {
                            this.setBrandImage(brandImage, this.brands.length - 1);
                        }

                        resetForm();
                        this.$refs.addBrandModal.toggle();

                    } catch (error) {
                        console.error('Error in saveBrand:', error);
                        setErrors({'brand_image': [error.message]});
                    }
                },

                setBrandImage(file, index) {
                    let dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);

                    if (this.brands[index].brand_image) {
                        this.brands[index].image = this.brands[index].brand_image;
                        delete this.brands[index].brand_image;
                    }

                    setTimeout(() => {
                        this.brands[index]._preview = URL.createObjectURL(file);
                    }, 0);
                },

                removeBrand(brand, index) {
                    this.$emitter.emit('open-confirm-modal', {
                        agree: () => {
                            if (brand.image) {
                                this.deletedBrands.push({
                                    image: brand.image
                                });
                            }
                            
                            this.brands.splice(index, 1);
                        }
                    });
                },
            },
        });
    </script>
@endPushOnce