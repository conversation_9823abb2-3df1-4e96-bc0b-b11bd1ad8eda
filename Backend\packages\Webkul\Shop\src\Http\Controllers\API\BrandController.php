<?php

namespace Webkul\Shop\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Shop\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;

class BrandController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected AttributeRepository $attributeRepository
    ) {}

    /**
     * Get brands for carousel display.
     */
    public function index(): JsonResource
    {
        $locale = app()->getLocale();
        
        // 查找brand属性
        $brandAttribute = $this->attributeRepository->findOneByField('code', 'brand');
        
        if (!$brandAttribute) {
            return new JsonResource(['brands' => []]);
        }
        
        // 获取品牌选项
        $brandOptions = $brandAttribute->options()
            ->with(['translations'])
            ->orderBy('sort_order')
            ->get();
        
        // 构建品牌数据
        $brands = $brandOptions->map(function ($brandOption) use ($locale) {
            return [
                'id' => $brandOption->id,
                'label' => $brandOption->translate($locale)?->label ?? $brandOption->admin_name,
                'sort_order' => $brandOption->sort_order,
                'swatch_value' => $brandOption->swatch_value,
                'swatch_value_url' => $brandOption->swatch_value ? Storage::url($brandOption->swatch_value) : null,
            ];
        });
        
        return new JsonResource(['brands' => $brands]);
    }
}
