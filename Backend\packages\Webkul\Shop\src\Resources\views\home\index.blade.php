@php
    $channel = core()->getCurrentChannel();
@endphp

<!-- SEO Meta Content -->
@push ('meta')
    <meta
        name="title"
        content="{{ $channel->home_seo['meta_title'] ?? '' }}"
    />

    <meta
        name="description"
        content="{{ $channel->home_seo['meta_description'] ?? '' }}"
    />

    <meta
        name="keywords"
        content="{{ $channel->home_seo['meta_keywords'] ?? '' }}"
    />
@endPush

<x-shop::layouts>
    <!-- Page Title -->
    <x-slot:title>
        {{  $channel->home_seo['meta_title'] ?? '' }}
    </x-slot>
    
    <!-- Loop over the theme customization -->
    @foreach ($customizations as $customization)
        @php ($data = $customization->options) @endphp

        <!-- Static content -->
        @switch ($customization->type)
            @case ($customization::IMAGE_CAROUSEL)
                <!-- Image Carousel -->
                <x-shop::carousel
                    :options="$data"
                    aria-label="{{ trans('shop::app.home.index.image-carousel') }}"
                />

                @break
            @case ($customization::STATIC_CONTENT)
                <!-- push style -->
                @if (! empty($data['css']))
                    @push ('styles')
                        <style>
                            {{ $data['css'] }}
                        </style>
                    @endpush
                @endif

                <!-- render html -->
                @if (! empty($data['html']))
                    {!! $data['html'] !!}
                @endif

                @break
            @case ($customization::CATEGORY_CAROUSEL)
                <!-- Categories carousel -->
                <x-shop::categories.carousel
                    :title="$data['title'] ?? ''"
                    :src="route('shop.api.categories.index', $data['filters'] ?? [])"
                    :navigation-link="route('shop.home.index')"
                    aria-label="{{ trans('shop::app.home.index.categories-carousel') }}"
                />

                @break
            @case ($customization::PRODUCT_CAROUSEL)
                <!-- Product Carousel -->
                <x-shop::products.carousel
                    :title="$data['title'] ?? ''"
                    :src="route('shop.api.products.index', $data['filters'] ?? [])"
                    :navigation-link="route('shop.search.index', $data['filters'] ?? [])"
                    aria-label="{{ trans('shop::app.home.index.product-carousel') }}"
                />

                @break
            @case ($customization::CMS_CONTENT)
                <!-- CMS Content Carousel -->
                <x-shop::cms.carousel
                    :title="$data['title'] ?? ''"
                    :src="route('shop.api.cms.pages.index', $data['filters'] ?? [])"
                    :navigation-link="$data['navigation_link'] ?? ''"
                    aria-label="{{ trans('shop::app.home.index.cms-carousel') }}"
                />
                @break

             @case ($customization::BRAND_CONTENT) 
                <!-- Brand Content Carousel -->
                <x-shop::brand.carousel
                     :title="$data['title'] ?? ''"
                    :src="route('shop.api.brands.index', $data['filters'] ?? [])"
                    :navigation-link="$data['navigation_link'] ?? ''"
                    aria-label="{{ trans('shop::app.home.index.brand-carousel') }}"
                />
                @break
        @endswitch
    @endforeach

    <!-- Help Floating Button + Modal Form (Componentized) -->
    <x-shop::help.contact-modal />

    <!-- Floating Quick-Contact Icons (Reusable Component) -->
    <x-shop::help.floating-icons />

        @pushOnce('scripts')
        <script type="module">
            // 通用轮播图功能 - 适用于静态内容中的轮播图
            document.addEventListener('DOMContentLoaded', function() {
                // 使用 setTimeout 确保静态内容已加载
                setTimeout(initCarousels, 100);
                
                function initCarousels() {
                    // 查找所有轮播图容器
                    const carousels = document.querySelectorAll('.carousel');
                    
                    carousels.forEach(carousel => {
                        initSingleCarousel(carousel);
                    });
                }
                
                function initSingleCarousel(carousel) {
                    const track = carousel.querySelector('.carousel-track');
                    const slides = carousel.querySelectorAll('.slide');
                    const nextButton = carousel.querySelector('.right-arrow, .next-arrow');
                    const prevButton = carousel.querySelector('.left-arrow, .prev-arrow');
                    const dotsContainer = carousel.querySelector('.carousel-dots');
                    
                    // 检查必要元素是否存在
                    if (!track || !slides.length) {
                        return;
                    }
                    
                    let currentSlide = 0;
                    const totalSlides = slides.length;
                    let autoPlayInterval = null;
                    let isHovered = false;
                    const autoPlayDuration = 5000; // 5秒
                    
                    // 初始化轮播
                    function init() {
                        // 创建导航点（如果容器存在）
                        if (dotsContainer) {
                            dotsContainer.innerHTML = '';
                            for (let i = 0; i < totalSlides; i++) {
                                const dot = document.createElement('button');
                                dot.classList.add('dot');
                                dot.setAttribute('aria-label', `转到第 ${i + 1} 张`);
                                if (i === 0) dot.classList.add('current-dot');
                                dot.addEventListener('click', () => goToSlide(i));
                                dotsContainer.appendChild(dot);
                            }
                        }
                        
                        // 设置初始状态
                        updateSlidePosition();
                        
                        // 绑定按钮事件
                        if (nextButton) {
                            nextButton.addEventListener('click', nextSlide);
                        }
                        if (prevButton) {
                            prevButton.addEventListener('click', previousSlide);
                        }
                        
                        // 绑定悬停事件
                        carousel.addEventListener('mouseenter', pauseAutoPlay);
                        carousel.addEventListener('mouseleave', resumeAutoPlay);
                        
                        // 启动自动播放
                        startAutoPlay();
                    }
                    
                    // 更新轮播位置
                    function updateSlidePosition() {
                        if (track) {
                            track.style.transform = `translateX(-${currentSlide * 100}%)`;
                        }
                        
                        // 更新slide状态
                        slides.forEach((slide, index) => {
                            slide.classList.toggle('current-slide', index === currentSlide);
                        });
                        
                        // 更新导航点
                        if (dotsContainer) {
                            const dots = dotsContainer.querySelectorAll('.dot');
                            dots.forEach((dot, index) => {
                                dot.classList.toggle('current-dot', index === currentSlide);
                            });
                        }
                    }
                    
                    // 下一张
                    function nextSlide() {
                        currentSlide = (currentSlide + 1) % totalSlides;
                        updateSlidePosition();
                    }
                    
                    // 上一张
                    function previousSlide() {
                        currentSlide = currentSlide === 0 ? totalSlides - 1 : currentSlide - 1;
                        updateSlidePosition();
                    }
                    
                    // 跳转到指定幻灯片
                    function goToSlide(index) {
                        currentSlide = index;
                        updateSlidePosition();
                    }
                    
                    // 自动播放
                    function startAutoPlay() {
                        autoPlayInterval = setInterval(() => {
                            if (!isHovered) {
                                nextSlide();
                            }
                        }, autoPlayDuration);
                    }
                    
                    // 停止自动播放
                    function stopAutoPlay() {
                        if (autoPlayInterval) {
                            clearInterval(autoPlayInterval);
                            autoPlayInterval = null;
                        }
                    }
                    
                    // 暂停自动播放
                    function pauseAutoPlay() {
                        isHovered = true;
                    }
                    
                    // 恢复自动播放
                    function resumeAutoPlay() {
                        isHovered = false;
                    }
                    
                    // 窗口大小调整处理
                    function handleResize() {
                        updateSlidePosition();
                    }
                    
                    // 绑定窗口调整事件
                    window.addEventListener('resize', handleResize);
                    
                    // 初始化轮播
                    init();
                }
                
                // 监听动态内容加载（如果有AJAX加载的内容）
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            // 检查新添加的节点是否包含轮播图
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1) { // 元素节点
                                    const newCarousels = node.querySelectorAll ? node.querySelectorAll('.carousel') : [];
                                    newCarousels.forEach(carousel => {
                                        initSingleCarousel(carousel);
                                    });
                                }
                            });
                        }
                    });
                });
                
                // 开始观察DOM变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            });
        </script>
    @endPushOnce
</x-shop::layouts>
